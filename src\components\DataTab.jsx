import React, { useState, useMemo, useEffect } from 'react';
import { 
  Eye, 
  RefreshCw, 
  Trash2, 
  Mail, 
  DollarSign, 
  BarChart3, 
  Search,
  Filter,
  Calendar,
  Download,
  FileText,
  TrendingUp,
  SortAsc,
  SortDesc,
  ChevronDown,
  X,
  AlertCircle,
  CheckCircle,
  Clock,
  ChevronLeft,
  ChevronRight,
  Loader,
  Globe,
  ArrowRightLeft
} from 'lucide-react';

const DataTab = ({ 
  loading = false,
  extractedData = [],
  lastScanResponse = null,
  calculateTotal = () => 0,
  clearProcessedEmails = () => {},
  setActiveTab = () => {} 
}) => {
  // State để lưu dữ liệu từ API
  const [apiData, setApiData] = useState([]);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState(null);
  const [error, setError] = useState(null);

  // Exchange rate states
  const [exchangeRates, setExchangeRates] = useState({});
  const [isLoadingRates, setIsLoadingRates] = useState(false);
  const [ratesError, setRatesError] = useState(null);
  const [lastRatesUpdate, setLastRatesUpdate] = useState(null);
  const [showExchangePanel, setShowExchangePanel] = useState(false);
  const [manualRates, setManualRates] = useState({});

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState({ from: '', to: '' });
  const [currencyFilter, setCurrencyFilter] = useState('all');
  const [filterNameFilter, setFilterNameFilter] = useState('all');
  const [accountFilter, setAccountFilter] = useState('all');
  const [amountRange, setAmountRange] = useState({ min: '', max: '' });
  
  // View states
  const [sortBy, setSortBy] = useState('emailDate');
  const [sortOrder, setSortOrder] = useState('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [showExportMenu, setShowExportMenu] = useState(false);

  // Function để fetch tỷ giá từ API
  const fetchExchangeRates = async () => {
    setIsLoadingRates(true);
    setRatesError(null);
    
    try {
      // Sử dụng API miễn phí exchangerate-api.com
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Chuyển đổi rates thành format thuận tiện (từ currency -> USD)
      const convertedRates = {};
      Object.entries(data.rates).forEach(([currency, rate]) => {
        convertedRates[currency] = 1 / rate; // Để chuyển từ currency sang USD
      });
      convertedRates.USD = 1; // USD = 1
      
      setExchangeRates(convertedRates);
      setLastRatesUpdate(new Date());
      
    } catch (err) {
      console.error('Error fetching exchange rates:', err);
      setRatesError(err.message);
      
      // Fallback rates nếu API không hoạt động
      const fallbackRates = {
        USD: 1,
        EUR: 1.1,
        GBP: 1.27,
        JPY: 0.0067,
        CNY: 0.14,
        VND: 0.000041,
        KRW: 0.00076,
        THB: 0.028,
        SGD: 0.74,
        MYR: 0.21,
        PHP: 0.018,
        IDR: 0.000066,
        INR: 0.012,
        AUD: 0.65,
        CAD: 0.74,
        CHF: 1.1,
        SEK: 0.092,
        NOK: 0.093,
        DKK: 0.147
      };
      setExchangeRates(fallbackRates);
      setLastRatesUpdate(new Date());
    } finally {
      setIsLoadingRates(false);
    }
  };

  // Function để chuyển đổi amount sang USD
  const convertToUSD = (amount, currency) => {
    if (!currency || currency === 'USD') return amount;
    
    // Kiểm tra manual rates trước
    if (manualRates[currency]) {
      return amount * manualRates[currency];
    }
    
    // Sử dụng API rates
    const rate = exchangeRates[currency];
    if (rate) {
      return amount * rate;
    }
    
    // Fallback: trả về amount gốc nếu không có rate
    return amount;
  };

  // Function để fetch dữ liệu từ API
  const fetchDataFromAPI = async () => {
    setIsLoadingData(true);
    setError(null);
    
    try {
      const response = await fetch('https://localhost:7126/api/EmailProcessing/dashboard-stats', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Chuyển đổi dữ liệu từ API thành format mà component expect
      const formattedData = data.recentEmails?.map(email => ({
        id: email.id,
        subject: email.subject,
        fromEmail: email.fromEmail,
        emailDate: email.emailDate,
        accountEmail: email.accountEmail,
        filterName: email.filterName,
        amount: email.amount,
        currency: email.currency,
        gmailId: email.gmailId,
        processedAt: email.processedAt,
        filterId: email.filterId,
        // Thêm amount đã chuyển đổi sang USD
        amountUSD: email.amount // Sẽ được tính lại sau khi có exchange rates
      })) || [];

      setApiData(formattedData);
      setLastFetchTime(new Date());
      
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.message);
    } finally {
      setIsLoadingData(false);
    }
  };

  // Update manual exchange rate
  const updateManualRate = (currency, rate) => {
    setManualRates(prev => ({
      ...prev,
      [currency]: parseFloat(rate) || 0
    }));
  };

  // Load dữ liệu khi component mount
  useEffect(() => {
    // Fetch exchange rates đầu tiên
    fetchExchangeRates();
    
    // Nếu có extractedData từ prop (từ quét mới), sử dụng nó
    if (extractedData && extractedData.length > 0) {
      setApiData(extractedData.map(item => ({
        ...item,
        amountUSD: item.amount // Sẽ được tính lại
      })));
      setLastFetchTime(new Date());
    } else {
      // Nếu không có extractedData, fetch từ API
      fetchDataFromAPI();
    }
  }, [extractedData]);

  // Cập nhật amountUSD khi exchange rates thay đổi
  useEffect(() => {
    if (Object.keys(exchangeRates).length > 0 || Object.keys(manualRates).length > 0) {
      setApiData(prev => prev.map(item => ({
        ...item,
        amountUSD: convertToUSD(item.amount, item.currency)
      })));
    }
  }, [exchangeRates, manualRates]);

  // Sử dụng apiData thay vì extractedData
  const currentData = apiData;

  // Get unique values for filter dropdowns
  const uniqueCurrencies = useMemo(() => {
    const currencies = [...new Set(currentData.map(item => item.currency || 'USD'))];
    return currencies.sort();
  }, [currentData]);

  const uniqueFilters = useMemo(() => {
    const filters = [...new Set(currentData.map(item => item.filterName || 'Unknown'))];
    return filters.sort();
  }, [currentData]);

  const uniqueAccounts = useMemo(() => {
    const accounts = [...new Set(currentData.map(item => item.accountEmail))];
    return accounts.sort();
  }, [currentData]);

  // Filtered and sorted data
  const filteredData = useMemo(() => {
    let filtered = currentData.filter(item => {
      // Search filter
      const searchMatch = !searchTerm || 
        item.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.fromEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.accountEmail.toLowerCase().includes(searchTerm.toLowerCase());

      // Date filter
      const itemDate = new Date(item.emailDate);
      const fromDate = dateFilter.from ? new Date(dateFilter.from) : null;
      const toDate = dateFilter.to ? new Date(dateFilter.to) : null;
      const dateMatch = (!fromDate || itemDate >= fromDate) && (!toDate || itemDate <= toDate);

      // Currency filter
      const currencyMatch = currencyFilter === 'all' || (item.currency || 'USD') === currencyFilter;

      // Filter name filter
      const filterMatch = filterNameFilter === 'all' || (item.filterName || 'Unknown') === filterNameFilter;

      // Account filter
      const accountMatch = accountFilter === 'all' || item.accountEmail === accountFilter;

      // Amount range filter (sử dụng USD amount cho filter)
      const minAmount = amountRange.min ? parseFloat(amountRange.min) : null;
      const maxAmount = amountRange.max ? parseFloat(amountRange.max) : null;
      const amountMatch = (!minAmount || item.amountUSD >= minAmount) && (!maxAmount || item.amountUSD <= maxAmount);

      return searchMatch && dateMatch && currencyMatch && filterMatch && accountMatch && amountMatch;
    });

    // Sort data
    filtered.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'emailDate':
          aValue = new Date(a.emailDate);
          bValue = new Date(b.emailDate);
          break;
        case 'amount':
          aValue = a.amountUSD; // Sắp xếp theo USD amount
          bValue = b.amountUSD;
          break;
        case 'subject':
          aValue = a.subject.toLowerCase();
          bValue = b.subject.toLowerCase();
          break;
        case 'fromEmail':
          aValue = a.fromEmail.toLowerCase();
          bValue = b.fromEmail.toLowerCase();
          break;
        default:
          aValue = a[sortBy];
          bValue = b[sortBy];
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [currentData, searchTerm, dateFilter, currencyFilter, filterNameFilter, accountFilter, amountRange, sortBy, sortOrder]);

  // Pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginatedData = useMemo(() => {
    const start = (currentPage - 1) * itemsPerPage;
    return filteredData.slice(start, start + itemsPerPage);
  }, [filteredData, currentPage, itemsPerPage]);

  // Calculate filtered totals (tất cả tính bằng USD)
  const filteredTotal = useMemo(() => {
    return filteredData.reduce((sum, item) => sum + item.amountUSD, 0);
  }, [filteredData]);

  const filteredAverage = useMemo(() => {
    return filteredData.length > 0 ? filteredTotal / filteredData.length : 0;
  }, [filteredTotal, filteredData.length]);

  const positiveTotal = useMemo(() => {
    return filteredData.filter(item => item.amountUSD > 0).reduce((sum, item) => sum + item.amountUSD, 0);
  }, [filteredData]);

  const negativeTotal = useMemo(() => {
    return filteredData.filter(item => item.amountUSD < 0).reduce((sum, item) => sum + item.amountUSD, 0);
  }, [filteredData]);

  // Calculate total from current data (tính bằng USD)
  const currentTotal = useMemo(() => {
    return currentData.reduce((sum, item) => sum + item.amountUSD, 0);
  }, [currentData]);

  // Clear all filters
  const clearAllFilters = () => {
    setSearchTerm('');
    setDateFilter({ from: '', to: '' });
    setCurrencyFilter('all');
    setFilterNameFilter('all');
    setAccountFilter('all');
    setAmountRange({ min: '', max: '' });
    setCurrentPage(1);
  };

  // Export functions
  const exportToCSV = () => {
    const headers = ['Date', 'Subject', 'From Email', 'Account', 'Filter', 'Amount', 'Currency', 'Amount USD'];
    const csvContent = [
      headers.join(','),
      ...filteredData.map(item => [
        new Date(item.emailDate).toLocaleDateString('vi-VN'),
        `"${item.subject}"`,
        item.fromEmail,
        item.accountEmail,
        item.filterName || 'Unknown',
        item.amount,
        item.currency || 'USD',
        item.amountUSD.toFixed(2)
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `email_data_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const exportToJSON = () => {
    const jsonContent = JSON.stringify(filteredData, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `email_data_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  };

  const generateReport = () => {
    const report = {
      generatedAt: new Date().toISOString(),
      lastFetchTime: lastFetchTime?.toISOString(),
      lastRatesUpdate: lastRatesUpdate?.toISOString(),
      exchangeRates: { ...exchangeRates, ...manualRates },
      summary: {
        totalRecords: filteredData.length,
        totalAmountUSD: filteredTotal,
        averageAmountUSD: filteredAverage,
        positiveAmountUSD: positiveTotal,
        negativeAmountUSD: negativeTotal
      },
      currencyBreakdown: Object.entries(
        filteredData.reduce((acc, item) => {
          const currency = item.currency || 'USD';
          if (!acc[currency]) {
            acc[currency] = { count: 0, totalOriginal: 0, totalUSD: 0 };
          }
          acc[currency].count++;
          acc[currency].totalOriginal += item.amount;
          acc[currency].totalUSD += item.amountUSD;
          return acc;
        }, {})
      ).map(([currency, stats]) => ({
        currency,
        count: stats.count,
        totalOriginal: stats.totalOriginal,
        totalUSD: stats.totalUSD,
        averageOriginal: stats.totalOriginal / stats.count,
        averageUSD: stats.totalUSD / stats.count,
        exchangeRate: exchangeRates[currency] || manualRates[currency] || 1
      })),
      data: filteredData
    };

    const jsonContent = JSON.stringify(report, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `email_report_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  };

  const toggleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const SortIcon = ({ field }) => {
    if (sortBy !== field) return null;
    return sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />;
  };

  const getAmountColor = (amount) => {
    if (amount > 0) return 'bg-green-100 text-green-800';
    if (amount < 0) return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      <div className="w-full bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Kết quả trích xuất dữ liệu</h2>
            {lastFetchTime && (
              <p className="text-sm text-gray-500 mt-1">
                Cập nhật lần cuối: {lastFetchTime.toLocaleString('vi-VN')}
              </p>
            )}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setShowExchangePanel(!showExchangePanel)}
              className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center space-x-2"
            >
              <Globe className="h-4 w-4" />
              <span>Tỷ giá</span>
            </button>
            <button
              onClick={fetchDataFromAPI}
              disabled={isLoadingData}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
            >
              {isLoadingData ? <Loader className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
              <span>Tải lại từ DB</span>
            </button>
            <button
              onClick={() => setActiveTab('scan')}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Mail className="h-4 w-4" />
              <span>Quét lại</span>
            </button>
            <button
              onClick={clearProcessedEmails}
              disabled={loading}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
            >
              <Trash2 className="h-4 w-4" />
              <span>Xóa dữ liệu</span>
            </button>
          </div>
        </div>

        {/* Exchange Rate Panel */}
        {showExchangePanel && (
          <div className="mb-6 bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <ArrowRightLeft className="h-5 w-5 text-orange-600" />
                <h3 className="font-semibold text-orange-800">Quản lý Tỷ giá</h3>
                {lastRatesUpdate && (
                  <span className="text-sm text-orange-600">
                    (Cập nhật: {lastRatesUpdate.toLocaleString('vi-VN')})
                  </span>
                )}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={fetchExchangeRates}
                  disabled={isLoadingRates}
                  className="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700 transition-colors flex items-center space-x-1"
                >
                  {isLoadingRates ? <Loader className="h-3 w-3 animate-spin" /> : <RefreshCw className="h-3 w-3" />}
                  <span>Cập nhật</span>
                </button>
                <button
                  onClick={() => setShowExchangePanel(false)}
                  className="bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-200 transition-colors"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            </div>

            {ratesError && (
              <div className="mb-4 bg-red-50 border border-red-200 rounded p-3">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <span className="text-red-800 text-sm">Lỗi tải tỷ giá: {ratesError}</span>
                </div>
                <p className="text-red-700 text-xs mt-1">Đang sử dụng tỷ giá dự phòng. Bạn có thể nhập thủ công bên dưới.</p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {uniqueCurrencies
                .filter(currency => currency !== 'USD')
                .map(currency => {
                  const apiRate = exchangeRates[currency];
                  const manualRate = manualRates[currency];
                  const currentRate = manualRate || apiRate;
                  
                  return (
                    <div key={currency} className="bg-white border border-orange-200 rounded p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-gray-800">{currency} → USD</span>
                        {manualRate && (
                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">Thủ công</span>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        {apiRate && (
                          <div className="text-sm text-gray-600">
                            API: {apiRate.toFixed(6)}
                          </div>
                        )}
                        
                        <div className="flex space-x-2">
                          <input
                            type="number"
                            step="0.000001"
                            placeholder={apiRate ? apiRate.toFixed(6) : "Nhập tỷ giá"}
                            value={manualRate || ''}
                            onChange={(e) => updateManualRate(currency, e.target.value)}
                            className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-orange-500"
                          />
                          {manualRate && (
                            <button
                              onClick={() => updateManualRate(currency, '')}
                              className="bg-red-100 text-red-600 px-2 py-1 rounded text-xs hover:bg-red-200"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          )}
                        </div>
                        
                        {currentRate && (
                          <div className="text-xs text-gray-500">
                            1 {currency} = {currentRate.toFixed(6)} USD
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
            </div>

            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
              <h4 className="font-medium text-blue-800 mb-2">💡 Hướng dẫn:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Tỷ giá được tự động cập nhật từ API exchangerate-api.com</li>
                <li>• Bạn có thể nhập tỷ giá thủ công để ghi đè tỷ giá API</li>
                <li>• Tất cả tính toán tổng được chuyển đổi sang USD để so sánh</li>
                <li>• Hiển thị trong bảng vẫn giữ nguyên loại tiền gốc</li>
              </ul>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <span className="text-red-800 font-medium">Lỗi khi tải dữ liệu:</span>
            </div>
            <p className="text-red-700 mt-1">{error}</p>
            <button
              onClick={fetchDataFromAPI}
              className="mt-2 bg-red-100 text-red-700 px-3 py-1 rounded hover:bg-red-200 transition-colors"
            >
              Thử lại
            </button>
          </div>
        )}

        {/* Loading State */}
        {isLoadingData && (
          <div className="text-center py-8">
            <Loader className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">Đang tải dữ liệu từ database...</p>
          </div>
        )}

        {currentData.length > 0 ? (
          <div className="space-y-4">
            {/* Search and Filter Bar */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex flex-wrap items-center gap-4 mb-4">
                {/* Search */}
                <div className="flex-1 min-w-64">
                  <div className="relative">
                    <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Tìm kiếm theo tiêu đề, email..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Toggle Filters Button */}
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="bg-white border border-gray-300 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
                >
                  <Filter className="h-4 w-4" />
                  <span>Bộ lọc</span>
                  <ChevronDown className={`h-4 w-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
                </button>

                {/* Clear Filters */}
                {(searchTerm || dateFilter.from || dateFilter.to || currencyFilter !== 'all' || filterNameFilter !== 'all' || accountFilter !== 'all' || amountRange.min || amountRange.max) && (
                  <button
                    onClick={clearAllFilters}
                    className="bg-red-100 text-red-700 px-4 py-2 rounded-lg hover:bg-red-200 transition-colors flex items-center space-x-2"
                  >
                    <X className="h-4 w-4" />
                    <span>Xóa bộ lọc</span>
                  </button>
                )}
              </div>

              {/* Advanced Filters */}
              {showFilters && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                  {/* Date Range */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      <Calendar className="h-4 w-4 inline mr-1" />
                      Khoảng thời gian
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="date"
                        value={dateFilter.from}
                        onChange={(e) => setDateFilter({ ...dateFilter, from: e.target.value })}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      />
                      <input
                        type="date"
                        value={dateFilter.to}
                        onChange={(e) => setDateFilter({ ...dateFilter, to: e.target.value })}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* Currency Filter */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Loại tiền tệ</label>
                    <select
                      value={currencyFilter}
                      onChange={(e) => setCurrencyFilter(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">Tất cả</option>
                      {uniqueCurrencies.map(currency => (
                        <option key={currency} value={currency}>{currency}</option>
                      ))}
                    </select>
                  </div>

                  {/* Filter Name */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Bộ lọc</label>
                    <select
                      value={filterNameFilter}
                      onChange={(e) => setFilterNameFilter(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">Tất cả</option>
                      {uniqueFilters.map(filter => (
                        <option key={filter} value={filter}>{filter}</option>
                      ))}
                    </select>
                  </div>

                  {/* Account Filter */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Tài khoản</label>
                    <select
                      value={accountFilter}
                      onChange={(e) => setAccountFilter(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">Tất cả</option>
                      {uniqueAccounts.map(account => (
                        <option key={account} value={account}>{account}</option>
                      ))}
                    </select>
                  </div>

                  {/* Amount Range */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">
                      <DollarSign className="h-4 w-4 inline mr-1" />
                      Khoảng số tiền (USD)
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="number"
                        placeholder="Tối thiểu"
                        value={amountRange.min}
                        onChange={(e) => setAmountRange({ ...amountRange, min: e.target.value })}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      />
                      <input
                        type="number"
                        placeholder="Tối đa"
                        value={amountRange.max}
                        onChange={(e) => setAmountRange({ ...amountRange, max: e.target.value })}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* Items per page */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Hiển thị</label>
                    <select
                      value={itemsPerPage}
                      onChange={(e) => {
                        setItemsPerPage(parseInt(e.target.value));
                        setCurrentPage(1);
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    >
                      <option value={10}>10 / trang</option>
                      <option value={25}>25 / trang</option>
                      <option value={50}>50 / trang</option>
                      <option value={100}>100 / trang</option>
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <Mail className="h-5 w-5 text-blue-600" />
                  <span className="font-medium text-blue-800">Hiển thị</span>
                </div>
                <div className="text-2xl font-bold text-blue-900 mt-1">
                  {filteredData.length} / {currentData.length}
                </div>
              </div>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  <span className="font-medium text-green-800">Thu nhập (USD)</span>
                </div>
                <div className="text-2xl font-bold text-green-900 mt-1">
                  +{positiveTotal.toLocaleString()}
                </div>
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <span className="font-medium text-red-800">Chi tiêu (USD)</span>
                </div>
                <div className="text-2xl font-bold text-red-900 mt-1">
                  {negativeTotal.toLocaleString()}
                </div>
              </div>
              
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5 text-purple-600" />
                  <span className="font-medium text-purple-800">TB (USD)</span>
                </div>
                <div className="text-2xl font-bold text-purple-900 mt-1">
                  {filteredAverage.toLocaleString()}
                </div>
              </div>

              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-orange-600" />
                  <span className="font-medium text-orange-800">Tỷ lệ lọc</span>
                </div>
                <div className="text-2xl font-bold text-orange-900 mt-1">
                  {currentData.length > 0 ? ((filteredData.length / currentData.length) * 100).toFixed(1) : 0}%
                </div>
              </div>
            </div>

            {/* Currency Summary for filtered data */}
            {filteredData.length > 0 && (
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h4 className="font-semibold text-gray-800 mb-3 flex items-center space-x-2">
                  <Globe className="h-5 w-5 text-blue-600" />
                  <span>💱 Tóm tắt theo loại tiền tệ (dữ liệu đã lọc)</span>
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                  {Object.entries(
                    filteredData.reduce((acc, item) => {
                      const currency = item.currency || 'USD';
                      if (!acc[currency]) {
                        acc[currency] = { totalOriginal: 0, totalUSD: 0 };
                      }
                      acc[currency].totalOriginal += item.amount;
                      acc[currency].totalUSD += item.amountUSD;
                      return acc;
                    }, {})
                  ).map(([currency, stats]) => {
                    const rate = exchangeRates[currency] || manualRates[currency] || 1;
                    return (
                      <div key={currency} className="bg-white rounded-lg p-3 border border-blue-200 shadow-sm text-center">
                        <div className="font-medium text-gray-900 text-sm mb-1">{currency}</div>
                        <div className="text-lg font-bold text-blue-700">
                          {stats.totalOriginal.toLocaleString()}
                        </div>
                        {currency !== 'USD' && (
                          <div className="text-xs text-gray-500 mt-1">
                            ≈ {stats.totalUSD.toLocaleString()} USD
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Data Table */}
            {filteredData.length > 0 ? (
              <>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th 
                          className="px-4 py-3 text-left text-sm font-semibold text-gray-900 cursor-pointer hover:bg-gray-100"
                          onClick={() => toggleSort('emailDate')}
                        >
                          <div className="flex items-center space-x-1">
                            <span>Ngày</span>
                            <SortIcon field="emailDate" />
                          </div>
                        </th>
                        <th 
                          className="px-4 py-3 text-left text-sm font-semibold text-gray-900 cursor-pointer hover:bg-gray-100"
                          onClick={() => toggleSort('subject')}
                        >
                          <div className="flex items-center space-x-1">
                            <span>Tiêu đề</span>
                            <SortIcon field="subject" />
                          </div>
                        </th>
                        <th 
                          className="px-4 py-3 text-left text-sm font-semibold text-gray-900 cursor-pointer hover:bg-gray-100"
                          onClick={() => toggleSort('fromEmail')}
                        >
                          <div className="flex items-center space-x-1">
                            <span>Người gửi</span>
                            <SortIcon field="fromEmail" />
                          </div>
                        </th>
                        <th 
                          className="px-4 py-3 text-left text-sm font-semibold text-gray-900 cursor-pointer hover:bg-gray-100"
                          onClick={() => toggleSort('amount')}
                        >
                          <div className="flex items-center space-x-1">
                            <span>Số tiền</span>
                            <SortIcon field="amount" />
                          </div>
                        </th>
                        <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Giá trị USD</th>
                        <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Tài khoản</th>
                        <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Bộ lọc</th>
                        <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900">Thao tác</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {paginatedData.map((item) => (
                        <tr key={item.id} className="hover:bg-gray-50">
                          <td className="px-4 py-3 text-sm text-gray-900">
                            <div className="flex items-center space-x-2">
                              <Clock className="h-4 w-4 text-gray-400" />
                              <span>{new Date(item.emailDate).toLocaleDateString('vi-VN')}</span>
                            </div>
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-900 max-w-xs truncate" title={item.subject}>
                            {item.subject}
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-600 max-w-xs truncate" title={item.fromEmail}>
                            {item.fromEmail}
                          </td>
                          <td className="px-4 py-3">
                            <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getAmountColor(item.amount)}`}>
                              {item.amount > 0 ? '+' : ''}{item.amount.toLocaleString()} {item.currency || 'USD'}
                            </span>
                          </td>
                          <td className="px-4 py-3">
                            <div className="flex items-center space-x-1">
                              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded ${getAmountColor(item.amountUSD)}`}>
                                {item.amountUSD > 0 ? '+' : ''}{item.amountUSD.toFixed(2)} USD
                              </span>
                              {item.currency !== 'USD' && (
                                <Globe className="h-3 w-3 text-blue-500" title="Đã quy đổi" />
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-600 max-w-xs truncate" title={item.accountEmail}>
                            {item.accountEmail}
                          </td>
                          <td className="px-4 py-3">
                            <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                              {item.filterName || 'Unknown Filter'}
                            </span>
                          </td>
                          <td className="px-4 py-3">
                            <button
                              onClick={() => {
                                const rate = exchangeRates[item.currency] || manualRates[item.currency] || 1;
                                const details = `
Chi tiết Email

Tiêu đề: ${item.subject}
Người gửi: ${item.fromEmail}
Ngày: ${new Date(item.emailDate).toLocaleString('vi-VN')}
Tài khoản: ${item.accountEmail}
Bộ lọc: ${item.filterName || 'Unknown'}

💰 Thông tin tài chính:
Số tiền gốc: ${item.amount.toLocaleString()} ${item.currency || 'USD'}
Giá trị USD: ${item.amountUSD.toFixed(2)} USD
Tỷ giá: ${rate.toFixed(6)} (${item.currency} → USD)

🔍 Thông tin kỹ thuật:
Gmail ID: ${item.gmailId}
Filter ID: ${item.filterId || 'N/A'}
Xử lý lúc: ${new Date(item.processedAt).toLocaleString('vi-VN')}
                                `;
                                alert(details);
                              }}
                              className="bg-blue-100 text-blue-700 px-3 py-1 rounded text-sm hover:bg-blue-200 transition-colors flex items-center space-x-1"
                            >
                              <Eye className="h-3 w-3" />
                              <span>Chi tiết</span>
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="text-sm text-gray-600">
                      Hiển thị {((currentPage - 1) * itemsPerPage) + 1} - {Math.min(currentPage * itemsPerPage, filteredData.length)} trong tổng số {filteredData.length} kết quả
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setCurrentPage(1)}
                        disabled={currentPage === 1}
                        className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                      >
                        <ChevronLeft className="h-4 w-4" />
                        <ChevronLeft className="h-4 w-4 -ml-2" />
                      </button>
                      
                      <button
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                      >
                        <ChevronLeft className="h-4 w-4" />
                        <span>Trước</span>
                      </button>
                      
                      {/* Page numbers */}
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                        if (pageNum <= totalPages) {
                          return (
                            <button
                              key={pageNum}
                              onClick={() => setCurrentPage(pageNum)}
                              className={`px-3 py-2 text-sm rounded-lg ${
                                pageNum === currentPage
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                              }`}
                            >
                              {pageNum}
                            </button>
                          );
                        }
                        return null;
                      })}
                      
                      <button
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                      >
                        <span>Sau</span>
                        <ChevronRight className="h-4 w-4" />
                      </button>

                      <button
                        onClick={() => setCurrentPage(totalPages)}
                        disabled={currentPage === totalPages}
                        className="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                      >
                        <ChevronRight className="h-4 w-4" />
                        <ChevronRight className="h-4 w-4 -ml-2" />
                      </button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Filter className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium">Không tìm thấy kết quả</p>
                <p className="text-sm">Thử điều chỉnh bộ lọc để xem thêm kết quả</p>
                <button
                  onClick={clearAllFilters}
                  className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Xóa tất cả bộ lọc
                </button>
              </div>
            )}

            {/* Export Options */}
            <div className="flex flex-wrap gap-4 pt-4 border-t relative">
              <button 
                onClick={exportToCSV}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>📊 Xuất CSV ({filteredData.length} records)</span>
              </button>
              
              <button 
                onClick={exportToJSON}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <FileText className="h-4 w-4" />
                <span>📄 Xuất JSON</span>
              </button>
              
              <button 
                onClick={generateReport}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
              >
                <TrendingUp className="h-4 w-4" />
                <span>📈 Tạo báo cáo chi tiết</span>
              </button>

              {/* Quick Actions */}
              <div className="ml-auto flex items-center space-x-2">
                <span className="text-sm text-gray-500">Thao tác nhanh:</span>
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setDateFilter({ from: '', to: new Date().toISOString().split('T')[0] });
                  }}
                  className="bg-yellow-100 text-yellow-700 px-3 py-1 rounded text-sm hover:bg-yellow-200 transition-colors"
                >
                  📅 Hôm nay
                </button>
                <button
                  onClick={() => {
                    const oneWeekAgo = new Date();
                    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
                    setDateFilter({ 
                      from: oneWeekAgo.toISOString().split('T')[0], 
                      to: new Date().toISOString().split('T')[0] 
                    });
                  }}
                  className="bg-yellow-100 text-yellow-700 px-3 py-1 rounded text-sm hover:bg-yellow-200 transition-colors"
                >
                  📅 7 ngày
                </button>
                <button
                  onClick={() => {
                    const oneMonthAgo = new Date();
                    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
                    setDateFilter({ 
                      from: oneMonthAgo.toISOString().split('T')[0], 
                      to: new Date().toISOString().split('T')[0] 
                    });
                  }}
                  className="bg-yellow-100 text-yellow-700 px-3 py-1 rounded text-sm hover:bg-yellow-200 transition-colors"
                >
                  📅 30 ngày
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            <Eye className="h-16 w-16 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium">Chưa có dữ liệu</p>
            <p className="text-sm mb-4">Quét email để xem kết quả trích xuất dữ liệu tại đây</p>
            <button
              onClick={() => setActiveTab('scan')}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Bắt đầu quét email
            </button>
          </div>
        )}
      </div>

      {/* Statistics Panel */}
      {currentData.length > 0 && (
        <div className="w-full bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Thống kê chi tiết</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Transaction Types */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-800">Loại giao dịch (USD)</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center p-2 bg-green-50 rounded">
                  <span className="text-green-700">Thu nhập</span>
                  <div className="text-right">
                    <div className="font-medium text-green-800">
                      {filteredData.filter(item => item.amountUSD > 0).length} giao dịch
                    </div>
                    <div className="text-xs text-green-600">
                      +{filteredData.filter(item => item.amountUSD > 0).reduce((sum, item) => sum + item.amountUSD, 0).toLocaleString()} USD
                    </div>
                  </div>
                </div>
                <div className="flex justify-between items-center p-2 bg-red-50 rounded">
                  <span className="text-red-700">Chi tiêu</span>
                  <div className="text-right">
                    <div className="font-medium text-red-800">
                      {filteredData.filter(item => item.amountUSD < 0).length} giao dịch
                    </div>
                    <div className="text-xs text-red-600">
                      {filteredData.filter(item => item.amountUSD < 0).reduce((sum, item) => sum + item.amountUSD, 0).toLocaleString()} USD
                    </div>
                  </div>
                </div>
                <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
                  <span className="text-gray-700">Trung tính</span>
                  <span className="font-medium text-gray-800">
                    {filteredData.filter(item => item.amountUSD === 0).length} giao dịch
                  </span>
                </div>
              </div>
            </div>

            {/* Filter Distribution */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-800">Phân bổ theo bộ lọc</h4>
              <div className="space-y-2">
                {Object.entries(
                  filteredData.reduce((acc, item) => {
                    const filter = item.filterName || 'Unknown';
                    if (!acc[filter]) {
                      acc[filter] = { count: 0, totalUSD: 0 };
                    }
                    acc[filter].count++;
                    acc[filter].totalUSD += item.amountUSD;
                    return acc;
                  }, {})
                ).slice(0, 5).map(([filter, stats]) => (
                  <div key={filter} className="flex justify-between items-center p-2 bg-blue-50 rounded">
                    <span className="text-blue-700 truncate">{filter}</span>
                    <div className="text-right">
                      <div className="font-medium text-blue-800">{stats.count}</div>
                      <div className="text-xs text-blue-600">{stats.totalUSD.toLocaleString()} USD</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Account Distribution */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-800">Phân bổ theo tài khoản</h4>
              <div className="space-y-2">
                {Object.entries(
                  filteredData.reduce((acc, item) => {
                    if (!acc[item.accountEmail]) {
                      acc[item.accountEmail] = { count: 0, totalUSD: 0 };
                    }
                    acc[item.accountEmail].count++;
                    acc[item.accountEmail].totalUSD += item.amountUSD;
                    return acc;
                  }, {})
                ).slice(0, 5).map(([account, stats]) => (
                  <div key={account} className="flex justify-between items-center p-2 bg-purple-50 rounded">
                    <span className="text-purple-700 truncate">{account}</span>
                    <div className="text-right">
                      <div className="font-medium text-purple-800">{stats.count}</div>
                      <div className="text-xs text-purple-600">{stats.totalUSD.toLocaleString()} USD</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

        </div>
      )}
    </div>
  );
};

export default DataTab;