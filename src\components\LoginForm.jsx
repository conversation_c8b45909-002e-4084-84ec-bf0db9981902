import React, { useState } from 'react';
import { RefreshCw } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import LSBLogo from '../assets/LSB.png';

const LoginForm = () => {
  const { login, loading } = useAuth();
  const [credentials, setCredentials] = useState({ username: '', password: '' });
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    
    if (!credentials.username || !credentials.password) {
      setError('Vui lòng nhập đầy đủ thông tin');
      return;
    }

    try {
      await login(credentials);
    } catch (error) {
      setError(error.message || 'Đăng nhập thất bại');
    }
  };

  return (
    <div className="min-h-screen w-screen bg-gray-50 flex items-center justify-center p-4" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
      <div className="w-full max-w-sm">
        {/* Logo */}
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 mb-6">
            <img src={LSBLogo} alt="LSB Logo" className="h-16 w-16 object-contain" />
          </div>

          <h1 className="font-bold text-gray-900 mb-2" style={{ fontSize: '30px', fontFamily: 'Inter, system-ui, sans-serif' }}>
            Sign in to LSB Email Manager
          </h1>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
              Email address
            </label>
            <input
              type="text"
              value={credentials.username}
              onChange={(e) => setCredentials({ ...credentials, username: e.target.value })}
              className="w-full px-3 py-3 bg-gray-100 border-0 rounded-md text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all duration-200"
              style={{ fontFamily: 'Inter, system-ui, sans-serif' }}
              placeholder="<EMAIL>"
              disabled={loading}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
              Password
            </label>
            <input
              type="password"
              value={credentials.password}
              onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
              className="w-full px-3 py-3 bg-gray-100 border-0 rounded-md text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all duration-200"
              style={{ fontFamily: 'Inter, system-ui, sans-serif' }}
              placeholder="••••••••"
              disabled={loading}
              required
            />
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-600 text-sm" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>{error}</p>
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-500 text-white py-3 px-4 rounded-md hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            style={{ fontFamily: 'Inter, system-ui, sans-serif' }}
          >
            {loading ? (
              <>
                <RefreshCw className="h-5 w-5 animate-spin mr-2" />
                Signing in...
              </>
            ) : (
              'Sign in'
            )}
          </button>
        </form>

        {/* Footer */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
            Don't have an account?{' '}
            <a href="#" className="text-blue-500 hover:text-blue-600 font-medium" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>
              Sign up
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
