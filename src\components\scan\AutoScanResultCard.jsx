import React from 'react';
import { 
  RefreshCw, 
  CheckCircle, 
  AlertCircle 
} from 'lucide-react';

const AutoScanResultCard = ({ result }) => {
  const formatDuration = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  const isRunning = result.status === 'running';
  const isSuccess = result.status === 'success';
  
  return (
    <div className={`rounded-lg p-4 border-2 ${
      isRunning ? 'bg-yellow-50 border-yellow-200' :
      isSuccess ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
    }`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          {isRunning ? (
            <RefreshCw className="h-5 w-5 text-yellow-600 animate-spin" />
          ) : isSuccess ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <AlertCircle className="h-5 w-5 text-red-600" />
          )}
          <span className={`font-medium ${
            isRunning ? 'text-yellow-800' : 
            isSuccess ? 'text-green-800' : 'text-red-800'
          }`}>
            {isRunning ? '🔄 Đang quét...' : 
             isSuccess ? '✅ Quét thành công' : '❌ Quét thất bại'}
          </span>
        </div>
        <span className="text-sm text-gray-500">
          {new Date(result.startTime).toLocaleString('vi-VN')}
        </span>
      </div>
      
      {!isRunning && (
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <div className="text-gray-600">📧 Email tìm thấy:</div>
            <div className="font-bold text-lg">{result.emails.processed}</div>
          </div>
          <div>
            <div className="text-gray-600">💰 Tổng giá trị:</div>
            <div className="font-bold text-lg">${result.amount.total.toFixed(2)}</div>
          </div>
          <div>
            <div className="text-gray-600">✅ Tài khoản thành công:</div>
            <div className="font-medium">{result.accounts.successful}/{result.accounts.total}</div>
          </div>
          <div>
            <div className="text-gray-600">⏱️ Thời gian:</div>
            <div className="font-medium">{formatDuration(result.duration)}</div>
          </div>
        </div>
      )}
      
      {isRunning && (
        <div className="text-center">
          <div className="text-yellow-700 font-medium">
            Đang quét {result.accounts.total} tài khoản với {result.filters.total} bộ lọc...
          </div>
          <div className="text-xs text-yellow-600 mt-1">
            Vui lòng chờ trong giây lát
          </div>
        </div>
      )}
    </div>
  );
};

export default AutoScanResultCard;
