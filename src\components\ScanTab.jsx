import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Play, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Settings,
  Zap,
  ArrowDownCircle,
  ArrowUpCircle,
  User,
  Filter,
  Pause,
  Timer,
  Plus,
  X,
  Mail,
  Bot,
  Sparkles,
  Target,
  Calendar
} from 'lucide-react';
import { apiCall } from '../utils/api';

const EnhancedScanTab = ({ 
  loading, 
  setLoading, 
  accounts, 
  filters, 
  extractedData,
  setExtractedData,
  setScanResults,
  setLastScanResponse,
  calculateTotal 
}) => {
  // Scan form states
  const [selectedAccount, setSelectedAccount] = useState('');
  const [selectedAccounts, setSelectedAccounts] = useState([]);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [selectedTemplateFilters, setSelectedTemplateFilters] = useState([]);
  const [scanStatus, setScanStatus] = useState('idle');
  const [activeTab, setActiveTab] = useState('manual');
  const [accountSelectionMode, setAccountSelectionMode] = useState('single');
  
  // Template filters from backend
  const [templateFilters, setTemplateFilters] = useState([]);
  
  // Auto-scan tracking states
  const [autoScanHistory, setAutoScanHistory] = useState([]);
  const [isAutoScanRunning, setIsAutoScanRunning] = useState(false);
  const [lastAutoScanResult, setLastAutoScanResult] = useState(null);
  
  // Simplified Auto-scan settings (only specific times)
  const [autoScanSettings, setAutoScanSettings] = useState({
    enabled: false,
    specificTimes: ['09:00', '15:00', '18:00'], // Default 3 times per day
    accounts: [],
    filters: [],
    templateFilters: [],
    lastRun: null,
    nextRun: null
  });
  
  // Auto-scan status
  const [autoScanStatus, setAutoScanStatus] = useState('stopped');
  const [autoScanTimer, setAutoScanTimer] = useState(null);
  const [nextScheduledTime, setNextScheduledTime] = useState(null);
  const [countdownTimer, setCountdownTimer] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState('');

  // Load template filters on component mount
  useEffect(() => {
    loadTemplateFilters();
    loadAutoScanSettings();
  }, []);

  // Auto-scan timer effect
  useEffect(() => {
    if (autoScanSettings.enabled && autoScanStatus === 'running') {
      startAutoScanTimer();
      startCountdownTimer();
    } else {
      stopAutoScanTimer();
      stopCountdownTimer();
    }
    
    return () => {
      stopAutoScanTimer();
      stopCountdownTimer();
    };
  }, [autoScanSettings.enabled, autoScanStatus, autoScanSettings.specificTimes]);

  // Countdown timer effect
  useEffect(() => {
    if (nextScheduledTime) {
      startCountdownTimer();
    } else {
      stopCountdownTimer();
    }
    
    return () => stopCountdownTimer();
  }, [nextScheduledTime]);

  // Start countdown timer
  const startCountdownTimer = () => {
    stopCountdownTimer();
    
    const updateCountdown = () => {
      if (!nextScheduledTime) {
        setTimeRemaining('Đang tính toán...');
        return;
      }
      
      const now = new Date();
      const timeDiff = nextScheduledTime - now;
      
      if (timeDiff <= 0) {
        setTimeRemaining('Sắp chạy...');
        return;
      }
      
      const hours = Math.floor(timeDiff / (60 * 60 * 1000));
      const minutes = Math.floor((timeDiff % (60 * 60 * 1000)) / (60 * 1000));
      const seconds = Math.floor((timeDiff % (60 * 1000)) / 1000);
      
      if (hours > 0) {
        setTimeRemaining(`${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
      } else if (minutes > 0) {
        setTimeRemaining(`${minutes}:${seconds.toString().padStart(2, '0')}`);
      } else {
        setTimeRemaining(`${seconds}s`);
      }
    };
    
    // Update immediately
    updateCountdown();
    
    // Update every second
    const timer = setInterval(updateCountdown, 1000);
    setCountdownTimer(timer);
  };

  // Stop countdown timer
  const stopCountdownTimer = () => {
    if (countdownTimer) {
      clearInterval(countdownTimer);
      setCountdownTimer(null);
    }
  };

  // Load template filters from API
  const loadTemplateFilters = async () => {
    try {
      const templates = await apiCall('/Filters/templates');
      setTemplateFilters(templates || []);
    } catch (error) {
      console.error('Error loading template filters:', error);
    }
  };

  // Load auto-scan settings
  const loadAutoScanSettings = async () => {
    try {
      const settings = await apiCall('/AutoScanSettings/settings');
      if (settings) {
        const simplifiedSettings = {
          enabled: settings.enabled || false,
          specificTimes: settings.specificTimes || ['09:00', '15:00', '18:00'],
          accounts: settings.accounts || [],
          filters: settings.filters || [],
          templateFilters: settings.templateFilters || [],
          lastRun: settings.lastRun,
          nextRun: settings.nextRun
        };
        setAutoScanSettings(simplifiedSettings);
        setAutoScanStatus(simplifiedSettings.enabled ? 'running' : 'stopped');
      }
    } catch (error) {
      console.error('Error loading auto-scan settings:', error);
    }
  };

  // Save auto-scan settings
  const saveAutoScanSettings = async (newSettings) => {
    try {
      await apiCall('/AutoScanSettings/settings', {
        method: 'POST',
        body: JSON.stringify(newSettings)
      });
      setAutoScanSettings(newSettings);
    } catch (error) {
      console.error('Error saving auto-scan settings:', error);
      alert('Lỗi khi lưu cài đặt tự động quét: ' + error.message);
    }
  };

  // Start auto-scan timer for specific times
  const startAutoScanTimer = () => {
    stopAutoScanTimer();
    scheduleNextScan();
  };

  // Schedule next scan
  const scheduleNextScan = () => {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    let nextTime = null;
    
    // Find next scheduled time today
    for (let i = 0; i < autoScanSettings.specificTimes.length; i++) {
      const timeStr = autoScanSettings.specificTimes[i];
      const [hours, minutes] = timeStr.split(':').map(Number);
      const timeInMinutes = hours * 60 + minutes;
      
      if (timeInMinutes > currentTime) {
        nextTime = timeStr;
        break;
      }
    }
    
    // If no more times today, schedule for tomorrow's first time
    if (!nextTime) {
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      const firstTime = autoScanSettings.specificTimes[0];
      const [hours, minutes] = firstTime.split(':').map(Number);
      tomorrow.setHours(hours, minutes, 0, 0);
      
      const timeUntilNextScan = tomorrow.getTime() - now.getTime();
      
      console.log(`📅 Scheduling next scan for tomorrow: ${tomorrow.toLocaleString()}`);
      console.log(`⏰ Time until next scan: ${Math.round(timeUntilNextScan / 1000 / 60)} minutes`);
      
      const timer = setTimeout(() => {
        console.log('🚀 Executing scheduled auto-scan for tomorrow');
        performAutoScan();
        scheduleNextScan(); // Schedule next after completion
      }, timeUntilNextScan);
      
      setAutoScanTimer(timer);
      setNextScheduledTime(tomorrow);
      setAutoScanSettings(prev => ({ ...prev, nextRun: tomorrow }));
      return;
    }
    
    // Schedule for next time today
    const [hours, minutes] = nextTime.split(':').map(Number);
    const nextScanDate = new Date(now);
    nextScanDate.setHours(hours, minutes, 0, 0);
    
    const timeUntilNextScan = nextScanDate.getTime() - now.getTime();
    
    console.log(`📅 Scheduling next scan for today: ${nextScanDate.toLocaleString()}`);
    console.log(`⏰ Time until next scan: ${Math.round(timeUntilNextScan / 1000 / 60)} minutes`);
    
    // ✅ Only schedule if time is in future
    if (timeUntilNextScan > 0) {
      const timer = setTimeout(() => {
        console.log('🚀 Executing scheduled auto-scan');
        performAutoScan();
        scheduleNextScan(); // Schedule next after completion
      }, timeUntilNextScan);
      
      setAutoScanTimer(timer);
      setNextScheduledTime(nextScanDate);
      setAutoScanSettings(prev => ({ ...prev, nextRun: nextScanDate }));
    } else {
      console.log('⚠️ Next time is in the past, rescheduling...');
      // If somehow the time is in the past, try again in 1 minute
      setTimeout(scheduleNextScan, 60000);
    }
  };

  // Stop auto-scan timer
  const stopAutoScanTimer = () => {
    if (autoScanTimer) {
      clearTimeout(autoScanTimer);
      setAutoScanTimer(null);
    }
  };

  // Hàm format thời gian duration
  const formatDuration = (milliseconds) => {
    if (milliseconds < 1000) return `${milliseconds}ms`;
    if (milliseconds < 60000) return `${Math.round(milliseconds / 1000)}s`;
    return `${Math.round(milliseconds / 60000)}m ${Math.round((milliseconds % 60000) / 1000)}s`;
  };

  // Hàm hiển thị thông báo auto-scan
  const showAutoScanNotification = (result) => {
    // Browser notification nếu được phép
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('🤖 Auto-scan hoàn thành!', {
        body: `Tìm thấy ${result.emails.processed} email mới, tổng giá trị $${result.amount.total.toFixed(2)}`,
        icon: '/favicon.ico' // Thay bằng icon của app
      });
    }
  };

  // ✅ FIXED: Perform auto-scan với đầy đủ data flow
  const performAutoScan = async () => {
    if (autoScanSettings.accounts.length === 0) {
      console.log('⚠️ Auto-scan skipped: No accounts configured');
      return;
    }

    console.log('🤖 Starting scheduled auto-scan...');
    console.log(`📊 Config: ${autoScanSettings.accounts.length} accounts, ${autoScanSettings.filters.length + autoScanSettings.templateFilters.length} filters`);

    // Đánh dấu đang chạy
    setIsAutoScanRunning(true);
    
    const scanStartTime = new Date();

    try {
      const requestData = {
        accountIds: autoScanSettings.accounts,
        fromDate: null,
        toDate: null,
        filterIds: autoScanSettings.filters || [],
        templateFilterIds: autoScanSettings.templateFilters || []
      };

      console.log('📤 Sending auto-scan request:', requestData);

      const response = await apiCall('/EmailProcessing/process-multiple-accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      console.log('📥 Auto-scan response received:', response);
      
      if (response && response.success) {
        // ✅ EXTRACT ALL PROCESSED EMAILS (same as manual scan)
        const allProcessedEmails = [];
        
        if (response.accountResults) {
          if (Array.isArray(response.accountResults)) {
            response.accountResults.forEach(accountResult => {
              if (accountResult.processedEmails) {
                allProcessedEmails.push(...accountResult.processedEmails);
              }
            });
          } else if (typeof response.accountResults === 'object') {
            Object.values(response.accountResults).forEach(accountResult => {
              if (accountResult && accountResult.processedEmails) {
                allProcessedEmails.push(...accountResult.processedEmails);
              }
            });
          }
        }

        // ✅ DEBUG: Log email breakdown by type
        const incomeEmails = allProcessedEmails.filter(email => email.amount > 0);
        const outcomeEmails = allProcessedEmails.filter(email => email.amount < 0);
        
        console.log('📊 Auto-scan email breakdown:');
        console.log(`   • Total emails: ${allProcessedEmails.length}`);
        console.log(`   • Income emails: ${incomeEmails.length}`);
        console.log(`   • Outcome emails: ${outcomeEmails.length}`);
        
        if (outcomeEmails.length > 0) {
          console.log('💰 Sample outcome emails:');
          outcomeEmails.slice(0, 3).forEach((email, index) => {
            console.log(`   ${index + 1}. ${email.subject}: ${email.amount} ${email.currency}`);
          });
        } else {
          console.warn('⚠️ AUTO-SCAN: No outcome emails found - this might be the issue!');
        }

        // ✅ CRITICAL: Update extractedData to pass to DataTab (SAME AS MANUAL SCAN)
        setExtractedData(allProcessedEmails);
        setScanResults(allProcessedEmails);
        setLastScanResponse(response);

        // ✅ Update auto-scan internal state
        const successfulCount = response.overallStats?.successfulAccounts || 0;
        const failedCount = response.overallStats?.failedAccounts || 0;
        
        const scanResult = {
          id: Date.now(),
          startTime: scanStartTime,
          endTime: new Date(),
          duration: Date.now() - scanStartTime.getTime(),
          status: 'success',
          accounts: {
            total: autoScanSettings.accounts.length,
            successful: successfulCount,
            failed: failedCount
          },
          emails: {
            processed: allProcessedEmails.length, // ✅ Use actual processed emails count
            newFound: allProcessedEmails.length
          },
          amount: {
            total: allProcessedEmails.reduce((sum, email) => sum + email.amount, 0), // ✅ Calculate from actual data
            currency: 'Mixed'
          },
          filters: {
            custom: autoScanSettings.filters.length,
            templates: autoScanSettings.templateFilters.length,
            total: autoScanSettings.filters.length + autoScanSettings.templateFilters.length
          }
        };

        // Save results
        setLastAutoScanResult(scanResult);
        setAutoScanHistory(prev => [scanResult, ...prev.slice(0, 9)]);
        
        // Update settings
        const now = new Date();
        const updatedSettings = { ...autoScanSettings, lastRun: now };
        setAutoScanSettings(updatedSettings);
        await saveAutoScanSettings(updatedSettings);

        // ✅ SUCCESS NOTIFICATION WITH INCOME/OUTCOME BREAKDOWN
        const durationText = scanResult.duration > 60000 
          ? `${Math.round(scanResult.duration / 60000)} phút` 
          : `${Math.round(scanResult.duration / 1000)} giây`;

        console.log(`🔔 Auto-scan completed in ${durationText}:`);
        console.log(`   📧 Total: ${allProcessedEmails.length} emails`);
        console.log(`   💚 Income: ${incomeEmails.length} emails (+$${incomeEmails.reduce((sum, e) => sum + Math.abs(e.amount), 0).toFixed(2)})`);
        console.log(`   💸 Outcome: ${outcomeEmails.length} emails (-$${Math.abs(outcomeEmails.reduce((sum, e) => sum + e.amount, 0)).toFixed(2)})`);
        console.log(`   💰 Net: $${allProcessedEmails.reduce((sum, e) => sum + e.amount, 0).toFixed(2)}`);

        // Show notification
        if (allProcessedEmails.length > 0) {
          showAutoScanNotification(scanResult);
          
          // ✅ Alert with breakdown
          alert(`🤖 Auto-scan hoàn thành!\n\n` +
                `📊 Kết quả:\n` +
                `• Tổng emails: ${allProcessedEmails.length}\n` +
                `• Thu nhập: ${incomeEmails.length} emails\n` +
                `• Chi tiêu: ${outcomeEmails.length} emails\n` +
                `• Tổng giá trị: $${allProcessedEmails.reduce((sum, e) => sum + e.amount, 0).toFixed(2)}\n` +
                `• Thời gian: ${durationText}`);
        }

      } else {
        throw new Error(response?.errorMessage || 'Auto-scan failed with unknown error');
      }

    } catch (error) {
      console.error('❌ Auto-scan error:', error);
      
      // Tạo kết quả lỗi
      const scanResult = {
        id: Date.now(),
        startTime: scanStartTime,
        endTime: new Date(),
        duration: Date.now() - scanStartTime.getTime(),
        status: 'error',
        error: error.message,
        accounts: {
          total: autoScanSettings.accounts.length,
          successful: 0,
          failed: autoScanSettings.accounts.length
        },
        emails: { processed: 0, newFound: 0 },
        amount: { total: 0, currency: 'USD' },
        filters: {
          custom: autoScanSettings.filters.length,
          templates: autoScanSettings.templateFilters.length,
          total: autoScanSettings.filters.length + autoScanSettings.templateFilters.length
        }
      };

      setLastAutoScanResult(scanResult);
      setAutoScanHistory(prev => [scanResult, ...prev.slice(0, 9)]);
      
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        accounts: autoScanSettings.accounts.length,
        filters: autoScanSettings.filters.length + autoScanSettings.templateFilters.length
      });
      
      // Don't stop auto-scan completely on error, just log and continue
      console.log('🔄 Auto-scan will continue on schedule despite this error');
      
    } finally {
      // Đánh dấu hoàn thành
      setIsAutoScanRunning(false);
    }
  };

  // Toggle auto-scan
  const toggleAutoScan = async () => {
    const newEnabled = !autoScanSettings.enabled;
    const newSettings = { ...autoScanSettings, enabled: newEnabled };
    
    await saveAutoScanSettings(newSettings);
    setAutoScanStatus(newEnabled ? 'running' : 'stopped');
  };

  // Add specific time
  const addSpecificTime = () => {
    const newTime = '12:00';
    const newTimes = [...autoScanSettings.specificTimes, newTime].sort();
    setAutoScanSettings(prev => ({ ...prev, specificTimes: newTimes }));
  };

  // Remove specific time
  const removeSpecificTime = (index) => {
    if (autoScanSettings.specificTimes.length > 1) {
      const newTimes = autoScanSettings.specificTimes.filter((_, i) => i !== index);
      setAutoScanSettings(prev => ({ ...prev, specificTimes: newTimes }));
    }
  };

  // Update specific time
  const updateSpecificTime = (index, newTime) => {
    const newTimes = [...autoScanSettings.specificTimes];
    newTimes[index] = newTime;
    newTimes.sort();
    setAutoScanSettings(prev => ({ ...prev, specificTimes: newTimes }));
  };

  // Start manual email scan
  const startEmailScan = async () => {
    const hasSelectedAccounts = accountSelectionMode === 'single' 
      ? selectedAccount 
      : selectedAccounts.length > 0;

    if (!hasSelectedAccounts) {
      alert('Vui lòng chọn ít nhất một tài khoản Gmail');
      return;
    }
    
    try {
      setScanStatus('scanning');
      setLoading(true);
      
      if (accountSelectionMode === 'single') {
        const requestData = {
          accountId: selectedAccount,
          fromDate: null,
          toDate: null,
          filterIds: selectedFilters || [],
          templateFilterIds: selectedTemplateFilters || []
        };

        const response = await apiCall('/EmailProcessing/process-single-account', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestData)
        });
        
        if (response.success) {
          setLastScanResponse(response);
          const allProcessedEmails = response.processedEmails || [];
          setExtractedData(allProcessedEmails);
          setScanResults(allProcessedEmails);
          setScanStatus('completed');
          
          alert(`✅ Quét Gmail hoàn thành!\n\n• Tổng email đã xử lý: ${response.totalProcessed || 0}\n• Email mới: ${response.newEmails || 0}\n• Tổng số tiền: ${response.totalAmount?.toFixed(2) || 0} USD`);
        } else {
          throw new Error(response.errorMessage || 'Processing failed');
        }
      } else {
        const requestData = {
          accountIds: selectedAccounts,
          fromDate: null,
          toDate: null,
          filterIds: selectedFilters || [],
          templateFilterIds: selectedTemplateFilters || []
        };

        const response = await apiCall('/EmailProcessing/process-multiple-accounts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestData)
        });
        
        if (response.success) {
          setLastScanResponse(response);
          
          const allProcessedEmails = [];
          
          if (response.accountResults) {
            if (Array.isArray(response.accountResults)) {
              response.accountResults.forEach(accountResult => {
                if (accountResult.processedEmails) {
                  allProcessedEmails.push(...accountResult.processedEmails);
                }
              });
            } else if (typeof response.accountResults === 'object') {
              Object.values(response.accountResults).forEach(accountResult => {
                if (accountResult && accountResult.processedEmails) {
                  allProcessedEmails.push(...accountResult.processedEmails);
                }
              });
            }
          }
          
          setExtractedData(allProcessedEmails);
          setScanResults(allProcessedEmails);
          setScanStatus('completed');
          
          const overallStats = response.overallStats || {};
          alert(`✅ Quét Gmail hoàn thành!\n\n• Tài khoản thành công: ${overallStats.successfulAccounts || 0}/${selectedAccounts.length}\n• Tổng email đã xử lý: ${overallStats.totalEmailsProcessed || 0}\n• Tổng số tiền: ${overallStats.totalAmount?.toFixed(2) || 0} USD`);
        } else {
          throw new Error(response.errorMessage || 'Processing failed');
        }
      }
      
    } catch (error) {
      console.error('Email scan error:', error);
      alert(`❌ Lỗi khi quét email:\n${error.message}`);
      setScanStatus('idle');
    } finally {
      setLoading(false);
    }
  };

  // Load recent processed emails
  const loadRecentProcessedEmails = async () => {
    try {
      setLoading(true);
      
      const response = await apiCall('/EmailProcessing/processed-emails?page=1&pageSize=50');
      
      if (response && response.items) {
        const transformedEmails = response.items.map(email => ({
          ...email,
          id: email.id || '',
          gmailId: email.gmailId || '',
          accountEmail: email.accountEmail || '',
          subject: email.subject || '',
          fromEmail: email.fromEmail || '',
          amount: email.amount || 0,
          currency: email.currency || 'USD',
          emailDate: email.emailDate || new Date().toISOString(),
          processedAt: email.processedAt || new Date().toISOString(),
          filterId: email.filterId || '',
          filterName: email.filterName || ''
        }));
        
        setExtractedData(transformedEmails);
        setScanResults(transformedEmails);
        setScanStatus('completed');
        
        const totalAmount = transformedEmails.reduce((sum, email) => sum + (email.amount || 0), 0);
        alert(`📧 Đã tải thành công!\n\n• Số emails: ${transformedEmails.length}\n• Tổng số tiền: $${totalAmount.toFixed(2)}`);
        
      } else {
        alert('📧 Không tìm thấy emails');
      }
    } catch (error) {
      console.error('Error loading recent emails:', error);
      alert(`❌ Lỗi khi tải emails:\n${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const getFilterTypeIcon = (type) => {
    switch(type) {
      case 'income': return <ArrowDownCircle className="h-4 w-4 text-emerald-600" />;
      case 'outcome': return <ArrowUpCircle className="h-4 w-4 text-rose-600" />;
      case 'template': return <Sparkles className="h-4 w-4 text-violet-600" />;
      default: return <Filter className="h-4 w-4 text-blue-600" />;
    }
  };

  const formatTime = (date) => {
    if (!date) return 'Chưa có';
    return new Date(date).toLocaleString('vi-VN');
  };

  const getTimeUntilNextScan = () => {
    return timeRemaining || 'Đang tính toán...';
  };

  // Component hiển thị kết quả auto-scan
  const AutoScanResultCard = ({ result }) => {
    const isSuccess = result.status === 'success';
    const isRunning = result.status === 'running';
    
    return (
      <div className={`rounded-lg p-4 border-2 ${
        isRunning ? 'bg-yellow-50 border-yellow-200' :
        isSuccess ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
      }`}>
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            {isRunning ? (
              <RefreshCw className="h-5 w-5 text-yellow-600 animate-spin" />
            ) : isSuccess ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-600" />
            )}
            <span className={`font-semibold ${
              isRunning ? 'text-yellow-900' :
              isSuccess ? 'text-green-900' : 'text-red-900'
            }`}>
              {isRunning ? 'Đang quét...' :
               isSuccess ? 'Quét thành công' : 'Quét thất bại'}
            </span>
          </div>
          <span className="text-sm text-gray-500">
            {result.endTime ? formatTime(result.endTime) : 'Đang chạy...'}
          </span>
        </div>
        
        {!isRunning && (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-gray-600">📧 Email tìm thấy:</div>
              <div className="font-bold text-lg">{result.emails.processed}</div>
            </div>
            <div>
              <div className="text-gray-600">💰 Tổng giá trị:</div>
              <div className="font-bold text-lg">${result.amount.total.toFixed(2)}</div>
            </div>
            <div>
              <div className="text-gray-600">✅ Tài khoản thành công:</div>
              <div className="font-medium">{result.accounts.successful}/{result.accounts.total}</div>
            </div>
            <div>
              <div className="text-gray-600">⏱️ Thời gian:</div>
              <div className="font-medium">{formatDuration(result.duration)}</div>
            </div>
          </div>
        )}
        
        {result.status === 'error' && (
          <div className="mt-3 p-2 bg-red-100 rounded text-red-800 text-sm">
            ❌ Lỗi: {result.error}
          </div>
        )}
      </div>
    );
  };

  // Component hiển thị lịch sử auto-scan
  const AutoScanHistorySection = () => {
    if (autoScanHistory.length === 0 && !isAutoScanRunning && !lastAutoScanResult) {
      return (
        <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
          <div className="text-center text-gray-500">
            <Bot className="h-12 w-12 mx-auto mb-2 text-gray-300" />
            <p>Chưa có lịch sử quét tự động</p>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-xl p-6 border border-slate-200">
        <div className="flex items-center space-x-2 mb-4">
          <Timer className="h-5 w-5 text-slate-600" />
          <h3 className="font-semibold text-slate-900">Lịch Sử Quét Tự Động</h3>
          {isAutoScanRunning && (
            <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium animate-pulse">
              Đang chạy
            </span>
          )}
        </div>
        
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {/* Hiển thị scan đang chạy */}
          {isAutoScanRunning && (
            <AutoScanResultCard result={{
              status: 'running',
              startTime: new Date(),
              accounts: { total: autoScanSettings.accounts.length },
              filters: { total: autoScanSettings.filters.length + autoScanSettings.templateFilters.length }
            }} />
          )}
          
          {/* Hiển thị kết quả gần nhất */}
          {lastAutoScanResult && !isAutoScanRunning && (
            <AutoScanResultCard result={lastAutoScanResult} />
          )}
          
          {/* Hiển thị lịch sử */}
          {autoScanHistory.map((result, index) => (
            <AutoScanResultCard key={result.id} result={result} />
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="datatab-main">
      <div className="datatab-card">
            {/* Header */}
        <div className="datatab-card-header">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                <Mail className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Quét Email Thông Minh
                </h2>
              </div>
            </div>
            
            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-gray-100 rounded-xl p-1">
              <button
                onClick={() => setActiveTab('manual')}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 ${
                  activeTab === 'manual' 
                    ? 'bg-white text-blue-600 shadow-md transform scale-105' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <Search className={`h-4 w-4 ${activeTab === 'manual' ? 'animate-pulse' : ''}`} style={{ animationDuration: '2s' }} />
                <span>Quét Thủ Công</span>
              </button>
              <button
                onClick={() => setActiveTab('auto')}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 ${
                  activeTab === 'auto' 
                    ? 'bg-white text-purple-600 shadow-md transform scale-105' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <Bot className={`h-4 w-4 ${activeTab === 'auto' ? 'animate-bounce' : ''}`} style={{ animationDuration: '2s' }} />
                <span>Tự Động Quét</span>
              </button>
            </div>
          </div>
        </div>

        <div className="datatab-card-body">
          {/* Manual Scan Tab */}
          {activeTab === 'manual' && (
            <div className="datatab-filter-grid">
              {/* Left Column - Configuration */}
              <div className="space-y-6">
                {/* Account Selection Mode */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                  <div className="flex items-center space-x-2 mb-4">
                    <User className="h-5 w-5 text-blue-600 animate-pulse" style={{ animationDuration: '3s' }} />
                    <h3 className="font-semibold text-blue-900">Chọn Tài Khoản</h3>
                  </div>
                  
                  <div className="flex space-x-2 bg-white rounded-lg p-1 mb-4">
                    <button
                      onClick={() => {
                        setAccountSelectionMode('single');
                        setSelectedAccounts([]);
                      }}
                      className={`flex-1 px-4 py-2 rounded-md font-medium transition-all duration-200 ${
                        accountSelectionMode === 'single' 
                          ? 'bg-blue-500 text-white shadow-md' 
                          : 'text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      Một Tài Khoản
                    </button>
                    <button
                      onClick={() => {
                        setAccountSelectionMode('multiple');
                        setSelectedAccount('');
                      }}
                      className={`flex-1 px-4 py-2 rounded-md font-medium transition-all duration-200 ${
                        accountSelectionMode === 'multiple' 
                          ? 'bg-blue-500 text-white shadow-md' 
                          : 'text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      Nhiều Tài Khoản
                    </button>
                  </div>

                  {/* Single Account Selection */}
                  {accountSelectionMode === 'single' && (
                    <select
                      value={selectedAccount}
                      onChange={(e) => setSelectedAccount(e.target.value)}
                      className="datatab-input datatab-select"
                    >
                      <option value="">-- Chọn tài khoản Gmail --</option>
                      {accounts.filter(acc => acc.isActive).map((account) => (
                        <option key={account.id} value={account.id}>
                          {account.displayName || account.email}
                        </option>
                      ))}
                    </select>
                  )}

                  {/* Multiple Account Selection */}
                  {accountSelectionMode === 'multiple' && (
                    <div>
                      <div className="text-sm text-blue-700 mb-3 font-medium">
                        Đã chọn: {selectedAccounts.length} tài khoản
                      </div>
                      <div className="space-y-2 max-h-40 overflow-y-auto bg-white rounded-lg border border-blue-200 p-3">
                        {accounts.filter(acc => acc.isActive).map((account) => (
                          <label key={account.id} className="flex items-center space-x-3 cursor-pointer hover:bg-blue-50 p-2 rounded-lg transition-colors">
                            <input
                              type="checkbox"
                              checked={selectedAccounts.includes(account.id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedAccounts([...selectedAccounts, account.id]);
                                } else {
                                  setSelectedAccounts(selectedAccounts.filter(id => id !== account.id));
                                }
                              }}
                              className="w-4 h-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <div className="flex-1">
                              <div className="font-medium text-gray-900">
                                {account.displayName || account.email}
                              </div>
                              <div className="text-sm text-gray-500">{account.email}</div>
                            </div>
                          </label>
                        ))}
                      </div>
                      
                      {accounts.filter(acc => acc.isActive).length > 0 && (
                        <div className="flex space-x-2 mt-3">
                          <button
                            onClick={() => setSelectedAccounts(accounts.filter(acc => acc.isActive).map(acc => acc.id))}
                            className="text-xs bg-blue-100 text-blue-800 px-3 py-1 rounded-full hover:bg-blue-200 transition-colors"
                          >
                            Chọn tất cả
                          </button>
                          <button
                            onClick={() => setSelectedAccounts([])}
                            className="text-xs bg-gray-100 text-gray-800 px-3 py-1 rounded-full hover:bg-gray-200 transition-colors"
                          >
                            Bỏ chọn tất cả
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Custom Filters */}
                <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-xl p-6 border border-emerald-100">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Filter className="h-5 w-5 text-emerald-600 animate-spin" style={{ animationDuration: '4s' }} />
                      <h3 className="font-semibold text-emerald-900">Bộ Lọc Tùy Chỉnh</h3>
                    </div>
                    <span className="bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full text-sm font-medium">
                      {selectedFilters.length} đã chọn
                    </span>
                  </div>
                  
                  <div className="space-y-3 bg-white rounded-lg border border-emerald-200 p-4">
                    {filters.filter(f => f.isActive).map((filter) => (
                      <div key={filter.id} className="border border-gray-200 rounded-lg p-4 hover:bg-emerald-50 transition-colors">
                        <label className="flex items-start space-x-3 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={selectedFilters.includes(filter.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedFilters([...selectedFilters, filter.id]);
                              } else {
                                setSelectedFilters(selectedFilters.filter(id => id !== filter.id));
                              }
                            }}
                            className="w-5 h-5 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded mt-1"
                          />
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              {getFilterTypeIcon(filter.type)}
                              <span className="font-semibold text-gray-900 text-lg">{filter.name}</span>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                filter.type === 'income' ? 'bg-green-100 text-green-800' :
                                filter.type === 'outcome' ? 'bg-red-100 text-red-800' :
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {filter.type === 'income' ? 'Thu nhập' :
                                 filter.type === 'outcome' ? 'Chi tiêu' : 'Khác'}
                              </span>
                            </div>
                            <div className="text-gray-600 mb-2">{filter.description || 'Bộ lọc tùy chỉnh'}</div>
                            {filter.keywords && (
                              <div className="flex flex-wrap gap-1">
                                {filter.keywords.split(',').slice(0, 5).map((keyword, idx) => (
                                  <span key={idx} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                                    {keyword.trim()}
                                  </span>
                                ))}
                                {filter.keywords.split(',').length > 5 && (
                                  <span className="text-gray-500 text-xs">+{filter.keywords.split(',').length - 5} từ khóa khác</span>
                                )}
                              </div>
                            )}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                  
                  {filters.filter(f => f.isActive).length === 0 && (
                    <div className="text-center py-6 text-gray-500">
                      <Filter className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                      <p>Chưa có bộ lọc tùy chỉnh</p>
                    </div>
                  )}
                </div>

                {/* Template Filters */}
                <div className="bg-gradient-to-br from-violet-50 to-purple-50 rounded-xl p-6 border border-violet-100">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Sparkles className="h-5 w-5 text-violet-600 animate-ping" style={{ animationDuration: '3s' }} />
                      <h3 className="font-semibold text-violet-900">Template Thông Minh</h3>
                    </div>
                    <span className="bg-violet-100 text-violet-800 px-3 py-1 rounded-full text-sm font-medium">
                      {selectedTemplateFilters.length} đã chọn
                    </span>
                  </div>

                  {/* Select All/None buttons */}
                  {templateFilters.length > 0 && (
                    <div className="flex space-x-2 mb-4">
                      <button
                        onClick={() => setSelectedTemplateFilters(templateFilters.map(t => t.id))}
                        className="text-xs bg-violet-100 text-violet-800 px-3 py-1 rounded-full hover:bg-violet-200 transition-colors"
                      >
                        Chọn tất cả
                      </button>
                      <button
                        onClick={() => setSelectedTemplateFilters([])}
                        className="text-xs bg-gray-100 text-gray-800 px-3 py-1 rounded-full hover:bg-gray-200 transition-colors"
                      >
                        Bỏ chọn tất cả
                      </button>
                    </div>
                  )}

                  <div className="space-y-2 bg-white rounded-lg border border-violet-200 p-4 max-h-64 overflow-y-auto">
                    {templateFilters.map((template) => (
                      <div key={template.id} className="border border-gray-200 rounded-lg p-3 hover:bg-violet-50 transition-colors">
                        <label className="flex items-center space-x-3 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={selectedTemplateFilters.includes(template.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedTemplateFilters([...selectedTemplateFilters, template.id]);
                              } else {
                                setSelectedTemplateFilters(selectedTemplateFilters.filter(id => id !== template.id));
                              }
                            }}
                            className="w-4 h-4 text-violet-600 focus:ring-violet-500 border-gray-300 rounded"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              {getFilterTypeIcon(template.type)}
                              <span className="font-medium text-gray-900 truncate">{template.name}</span>
                              <span className="text-xs bg-violet-100 text-violet-800 px-2 py-1 rounded font-medium flex-shrink-0">
                                {template.platform}
                              </span>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ${
                                template.type === 'income' ? 'bg-green-100 text-green-800' :
                                template.type === 'outcome' ? 'bg-red-100 text-red-800' :
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {template.type === 'income' ? 'Thu nhập' : 'Chi tiêu'}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-gray-600">
                                {template.language === 'vi' ? '🇻🇳 Tiếng Việt' : '🇺🇸 English'}
                              </span>
                              {template.keywords && (
                                <span className="text-xs text-gray-500">
                                  • {template.keywords.split(',').length} từ khóa
                                </span>
                              )}
                            </div>
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>

                  {templateFilters.length === 0 && (
                    <div className="text-center py-6 text-gray-500">
                      <Sparkles className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                      <p>Đang tải templates...</p>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <button
                    onClick={startEmailScan}
                    disabled={loading || 
                      (accountSelectionMode === 'single' && !selectedAccount) ||
                      (accountSelectionMode === 'multiple' && selectedAccounts.length === 0) ||
                      (selectedFilters.length === 0 && selectedTemplateFilters.length === 0)}
                    className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-4 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center space-x-3 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    {scanStatus === 'scanning' ? (
                      <>
                        <RefreshCw className="h-5 w-5 animate-spin" />
                        <span>Đang quét...</span>
                      </>
                    ) : (
                      <>
                        <Play className="h-5 w-5" />
                        <span>Bắt Đầu Quét Email</span>
                      </>
                    )}
                  </button>
                  
                  <button
                    onClick={loadRecentProcessedEmails}
                    disabled={loading}
                    className="w-full bg-white border-2 border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 px-6 py-3 rounded-xl font-medium transition-all duration-200 flex items-center justify-center space-x-2 disabled:opacity-50 hover:shadow-md"
                  >
                    <RefreshCw className="h-4 w-4" />
                    <span>Tải Email Đã Xử Lý</span>
                  </button>
                </div>
              </div>

              {/* Right Column - Status & Summary */}
              <div className="space-y-6">
                {/* Scan Status */}
                <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-xl p-6 border border-slate-200">
                  <div className="flex items-center space-x-2 mb-4">
                    <Target className="h-5 w-5 text-slate-600 animate-pulse" style={{ animationDuration: '3s' }} />
                    <h3 className="font-semibold text-slate-900">Trạng Thái Quét</h3>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-slate-600">Chế độ:</span>
                      <span className="font-semibold text-slate-900">
                        {accountSelectionMode === 'single' ? 'Một tài khoản' : 'Nhiều tài khoản'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-600">Tài khoản:</span>
                      <span className="font-semibold text-slate-900">
                        {accountSelectionMode === 'single' 
                          ? (selectedAccount ? '1 đã chọn' : 'Chưa chọn')
                          : `${selectedAccounts.length} đã chọn`
                        }
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-600">Bộ lọc tùy chỉnh:</span>
                      <span className="font-semibold text-emerald-600">{selectedFilters.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-600">Template thông minh:</span>
                      <span className="font-semibold text-violet-600">{selectedTemplateFilters.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-600">Tổng bộ lọc:</span>
                      <span className="font-bold text-blue-600">{selectedFilters.length + selectedTemplateFilters.length}</span>
                    </div>
                    
                    <div className="pt-3 border-t border-slate-200">
                      <div className="flex justify-between items-center">
                        <span className="text-slate-600">Trạng thái:</span>
                        <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                          scanStatus === 'scanning' ? 'bg-yellow-100 text-yellow-800' :
                          scanStatus === 'completed' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-600'
                        }`}>
                          {scanStatus === 'scanning' ? '🔄 Đang quét' :
                           scanStatus === 'completed' ? '✅ Hoàn thành' :
                           '⏳ Chờ quét'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Scan Progress */}
                {scanStatus === 'scanning' && (
                  <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-6 border border-yellow-200">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-2 border-yellow-400 border-t-transparent"></div>
                      <h3 className="font-semibold text-yellow-900">Đang Quét Email</h3>
                    </div>
                    <p className="text-yellow-800 mb-3">
                      Hệ thống đang phân tích email từ {accountSelectionMode === 'single' ? '1 tài khoản' : selectedAccounts.length + ' tài khoản'} với {selectedFilters.length + selectedTemplateFilters.length} bộ lọc...
                    </p>
                    <div className="bg-yellow-100 rounded-lg p-3">
                      <div className="text-sm text-yellow-800">
                        💡 <strong>Lưu ý:</strong> Vui lòng không đóng trình duyệt trong quá trình quét
                      </div>
                    </div>
                  </div>
                )}

                {/* Scan Results */}
                {scanStatus === 'completed' && (
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
                    <div className="flex items-center space-x-2 mb-4">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                      <h3 className="font-semibold text-green-900">Quét Hoàn Thành!</h3>
                    </div>
                    <p className="text-green-800 mb-4">
                      Đã tìm thấy <strong>{extractedData.length}</strong> email phù hợp với các bộ lọc đã chọn.
                    </p>
                    {extractedData.length > 0 && (
                      <div className="bg-white rounded-lg p-4 border border-green-200">
                        <div className="text-center">
                          <div className="text-sm text-green-700 mb-1">💰 Tổng Số Tiền</div>
                          <div className="text-3xl font-bold text-green-900">
                            ${calculateTotal().toLocaleString()}
                          </div>
                          <div className="text-sm text-green-600 mt-1">USD</div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Requirements Notice */}
                {((accountSelectionMode === 'single' && selectedAccount) || (accountSelectionMode === 'multiple' && selectedAccounts.length > 0)) && 
                 (selectedFilters.length === 0 && selectedTemplateFilters.length === 0) && (
                  <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 border border-orange-200">
                    <div className="flex items-center space-x-2 mb-3">
                      <AlertCircle className="h-5 w-5 text-orange-600" />
                      <h3 className="font-semibold text-orange-900">Cần Chọn Bộ Lọc</h3>
                    </div>
                    <p className="text-orange-800">
                      Vui lòng chọn ít nhất 1 bộ lọc để đảm bảo quét chính xác số tiền và giao dịch.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Auto Scan Tab */}
          {activeTab === 'auto' && (
            <div className="datatab-filter-grid">
              {/* Left Column - Auto Settings */}
              <div className="space-y-6">
                {/* Auto Scan Control */}
                <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-6 border border-purple-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Bot className="h-6 w-6 text-purple-600 animate-bounce" style={{ animationDuration: '3s' }} />
                      <h3 className="font-semibold text-purple-900">Tự Động Quét</h3>
                    </div>
                    <button
                      onClick={toggleAutoScan}
                      className={`px-6 py-2 rounded-full font-semibold transition-all duration-200 flex items-center space-x-2 ${
                        autoScanSettings.enabled 
                          ? 'bg-red-500 hover:bg-red-600 text-white shadow-lg' 
                          : 'bg-green-500 hover:bg-green-600 text-white shadow-lg'
                      }`}
                    >
                      {autoScanSettings.enabled ? (
                        <>
                          <Pause className="h-4 w-4" />
                          <span>Tắt</span>
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4" />
                          <span>Bật</span>
                        </>
                      )}
                    </button>
                  </div>
                  
                  {/* Schedule Times */}
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-purple-600 animate-spin" style={{ animationDuration: '4s' }} />
                          <span className="font-medium text-purple-900">Giờ Quét Hàng Ngày</span>
                        </div>
                        <button
                          onClick={addSpecificTime}
                          className="bg-purple-100 hover:bg-purple-200 text-purple-800 px-3 py-1 rounded-lg text-sm flex items-center space-x-1 transition-colors"
                        >
                          <Plus className="h-3 w-3" />
                          <span>Thêm</span>
                        </button>
                      </div>
                      
                      <div className="space-y-2">
                        {autoScanSettings.specificTimes.map((time, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <input
                              type="time"
                              value={time}
                              onChange={(e) => updateSpecificTime(index, e.target.value)}
                              className="flex-1 px-3 py-2 border border-purple-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            />
                            {autoScanSettings.specificTimes.length > 1 && (
                              <button
                                onClick={() => removeSpecificTime(index)}
                                className="bg-red-100 hover:bg-red-200 text-red-600 p-2 rounded-lg transition-colors"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Auto Scan Accounts */}
                <div className="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl p-6 border border-blue-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <User className="h-5 w-5 text-blue-600 animate-pulse" style={{ animationDuration: '3s' }} />
                      <h3 className="font-semibold text-blue-900">Tài Khoản Tự Động</h3>
                    </div>
                    <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                      {autoScanSettings.accounts.length} đã chọn
                    </span>
                  </div>
                  
                  <div className="space-y-2 max-h-40 overflow-y-auto bg-white rounded-lg border border-blue-200 p-3">
                    {accounts.filter(acc => acc.isActive).map((account) => (
                      <label key={account.id} className="flex items-center space-x-3 cursor-pointer hover:bg-blue-50 p-2 rounded-lg transition-colors">
                        <input
                          type="checkbox"
                          checked={autoScanSettings.accounts.includes(account.id)}
                          onChange={(e) => {
                            const newAccounts = e.target.checked
                              ? [...autoScanSettings.accounts, account.id]
                              : autoScanSettings.accounts.filter(id => id !== account.id);
                            setAutoScanSettings(prev => ({ ...prev, accounts: newAccounts }));
                          }}
                          className="w-4 h-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">
                            {account.displayName || account.email}
                          </div>
                          <div className="text-sm text-gray-500">{account.email}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                  
                  {accounts.filter(acc => acc.isActive).length > 0 && (
                    <div className="flex space-x-2 mt-3">
                      <button
                        onClick={() => setAutoScanSettings(prev => ({ 
                          ...prev, 
                          accounts: accounts.filter(acc => acc.isActive).map(acc => acc.id) 
                        }))}
                        className="text-xs bg-blue-100 text-blue-800 px-3 py-1 rounded-full hover:bg-blue-200 transition-colors"
                      >
                        Chọn tất cả
                      </button>
                      <button
                        onClick={() => setAutoScanSettings(prev => ({ ...prev, accounts: [] }))}
                        className="text-xs bg-gray-100 text-gray-800 px-3 py-1 rounded-full hover:bg-gray-200 transition-colors"
                      >
                        Bỏ chọn tất cả
                      </button>
                    </div>
                  )}
                </div>

                {/* Auto Scan Filters */}
                <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-xl p-6 border border-emerald-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Filter className="h-5 w-5 text-emerald-600 animate-spin" style={{ animationDuration: '4s' }} />
                      <h3 className="font-semibold text-emerald-900">Bộ Lọc Tự Động</h3>
                    </div>
                    <span className="bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full text-sm font-medium">
                      {autoScanSettings.filters.length} đã chọn
                    </span>
                  </div>
                  
                  <div className="space-y-3 bg-white rounded-lg border border-emerald-200 p-4">
                    {filters.filter(f => f.isActive).map((filter) => (
                      <div key={filter.id} className="border border-gray-200 rounded-lg p-4 hover:bg-emerald-50 transition-colors">
                        <label className="flex items-start space-x-3 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={autoScanSettings.filters.includes(filter.id)}
                            onChange={(e) => {
                              const newFilters = e.target.checked
                                ? [...autoScanSettings.filters, filter.id]
                                : autoScanSettings.filters.filter(id => id !== filter.id);
                              setAutoScanSettings(prev => ({ ...prev, filters: newFilters }));
                            }}
                            className="w-5 h-5 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded mt-1"
                          />
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              {getFilterTypeIcon(filter.type)}
                              <span className="font-semibold text-gray-900 text-lg">{filter.name}</span>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                filter.type === 'income' ? 'bg-green-100 text-green-800' :
                                filter.type === 'outcome' ? 'bg-red-100 text-red-800' :
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {filter.type === 'income' ? 'Thu nhập' :
                                 filter.type === 'outcome' ? 'Chi tiêu' : 'Khác'}
                              </span>
                            </div>
                            <div className="text-gray-600 mb-2">{filter.description || 'Bộ lọc tùy chỉnh'}</div>
                            {filter.keywords && (
                              <div className="flex flex-wrap gap-1">
                                {filter.keywords.split(',').slice(0, 5).map((keyword, idx) => (
                                  <span key={idx} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                                    {keyword.trim()}
                                  </span>
                                ))}
                                {filter.keywords.split(',').length > 5 && (
                                  <span className="text-gray-500 text-xs">+{filter.keywords.split(',').length - 5} từ khóa khác</span>
                                )}
                              </div>
                            )}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                  
                  {filters.filter(f => f.isActive).length > 0 && (
                    <div className="flex space-x-2 mt-3">
                      <button
                        onClick={() => setAutoScanSettings(prev => ({ 
                          ...prev, 
                          filters: filters.filter(f => f.isActive).map(f => f.id) 
                        }))}
                        className="text-xs bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full hover:bg-emerald-200 transition-colors"
                      >
                        Chọn tất cả
                      </button>
                      <button
                        onClick={() => setAutoScanSettings(prev => ({ ...prev, filters: [] }))}
                        className="text-xs bg-gray-100 text-gray-800 px-3 py-1 rounded-full hover:bg-gray-200 transition-colors"
                      >
                        Bỏ chọn tất cả
                      </button>
                    </div>
                  )}
                </div>

                {/* Auto Templates */}
                <div className="bg-gradient-to-br from-violet-50 to-purple-50 rounded-xl p-6 border border-violet-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Sparkles className="h-5 w-5 text-violet-600 animate-ping" style={{ animationDuration: '3s' }} />
                      <h3 className="font-semibold text-violet-900">Template</h3>
                    </div>
                    <span className="bg-violet-100 text-violet-800 px-3 py-1 rounded-full text-sm font-medium">
                      {autoScanSettings.templateFilters.length} đã chọn
                    </span>
                  </div>

                  {/* Select All/None buttons */}
                  {templateFilters.length > 0 && (
                    <div className="flex space-x-2 mb-4">
                      <button
                        onClick={() => setAutoScanSettings(prev => ({
                          ...prev,
                          templateFilters: templateFilters.map(t => t.id)
                        }))}
                        className="text-xs bg-violet-100 text-violet-800 px-3 py-1 rounded-full hover:bg-violet-200 transition-colors"
                      >
                        Chọn tất cả
                      </button>
                      <button
                        onClick={() => setAutoScanSettings(prev => ({ ...prev, templateFilters: [] }))}
                        className="text-xs bg-gray-100 text-gray-800 px-3 py-1 rounded-full hover:bg-gray-200 transition-colors"
                      >
                        Bỏ chọn tất cả
                      </button>
                    </div>
                  )}

                  <div className="space-y-2 bg-white rounded-lg border border-violet-200 p-4 max-h-64 overflow-y-auto">
                    {templateFilters.map((template) => (
                      <div key={template.id} className="border border-gray-200 rounded-lg p-3 hover:bg-violet-50 transition-colors">
                        <label className="flex items-center space-x-3 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={autoScanSettings.templateFilters.includes(template.id)}
                            onChange={(e) => {
                              const newTemplateFilters = e.target.checked
                                ? [...autoScanSettings.templateFilters, template.id]
                                : autoScanSettings.templateFilters.filter(id => id !== template.id);
                              setAutoScanSettings(prev => ({ ...prev, templateFilters: newTemplateFilters }));
                            }}
                            className="w-4 h-4 text-violet-600 focus:ring-violet-500 border-gray-300 rounded"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              {getFilterTypeIcon(template.type)}
                              <span className="font-medium text-gray-900 truncate">{template.name}</span>
                              <span className="text-xs bg-violet-100 text-violet-800 px-2 py-1 rounded font-medium flex-shrink-0">
                                {template.platform}
                              </span>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ${
                                template.type === 'income' ? 'bg-green-100 text-green-800' :
                                template.type === 'outcome' ? 'bg-red-100 text-red-800' :
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {template.type === 'income' ? 'Thu nhập' : 'Chi tiêu'}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-gray-600">
                                {template.language === 'vi' ? '🇻🇳 Tiếng Việt' : '🇺🇸 English'}
                              </span>
                              {template.keywords && (
                                <span className="text-xs text-gray-500">
                                  • {template.keywords.split(',').length} từ khóa
                                </span>
                              )}
                            </div>
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Save Settings */}
                <button
                  onClick={() => saveAutoScanSettings(autoScanSettings)}
                  className="w-full bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white px-6 py-4 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <Settings className="h-5 w-5" />
                  <span>Lưu Cài Đặt Tự Động</span>
                </button>
              </div>

              {/* Right Column - Auto Status */}
              <div className="space-y-6">
                {/* Auto Status */}
                <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-xl p-6 border border-slate-200">
                  <div className="flex items-center space-x-2 mb-4">
                    <Bot className="h-5 w-5 text-slate-600 animate-bounce" style={{ animationDuration: '3s' }} />
                    <h3 className="font-semibold text-slate-900">Trạng Thái Tự Động</h3>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-slate-600">Trạng thái:</span>
                      <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                        autoScanStatus === 'running' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                      }`}>
                        {autoScanStatus === 'running' ? '🟢 Đang chạy' : '🔴 Đã dừng'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-600">Số lần quét/ngày:</span>
                      <span className="font-semibold text-slate-900">{autoScanSettings.specificTimes.length} lần</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-600">Tài khoản:</span>
                      <span className="font-semibold text-blue-600">{autoScanSettings.accounts.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-600">Bộ lọc:</span>
                      <span className="font-semibold text-emerald-600">{autoScanSettings.filters.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-600">Template:</span>
                      <span className="font-semibold text-violet-600">{autoScanSettings.templateFilters.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-slate-600">Tổng bộ lọc:</span>
                      <span className="font-bold text-purple-600">{autoScanSettings.filters.length + autoScanSettings.templateFilters.length}</span>
                    </div>
                    
                    <div className="pt-3 border-t border-slate-200">
                      <div className="flex justify-between items-center">
                        <span className="text-slate-600">Lần chạy cuối:</span>
                        <span className="font-semibold text-slate-900 text-sm">
                          {formatTime(autoScanSettings.lastRun)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <span className="text-slate-600">Lần chạy tiếp:</span>
                        <span className="font-semibold text-slate-900 text-sm">
                          {autoScanSettings.enabled ? formatTime(autoScanSettings.nextRun) : 'Đã tắt'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Schedule Today */}
                {autoScanSettings.enabled && (
                  <div className="bg-gradient-to-br from-indigo-50 to-blue-50 rounded-xl p-6 border border-indigo-200">
                    <div className="flex items-center space-x-2 mb-4">
                      <Calendar className="h-5 w-5 text-indigo-600 animate-pulse" style={{ animationDuration: '3s' }} />
                      <h3 className="font-semibold text-indigo-900">Lịch Quét Hôm Nay</h3>
                    </div>
                    
                    <div className="space-y-2">
                      {autoScanSettings.specificTimes.map((time, index) => {
                        const now = new Date();
                        const [hours, minutes] = time.split(':').map(Number);
                        const scheduledTime = new Date(now);
                        scheduledTime.setHours(hours, minutes, 0, 0);
                        const isPast = scheduledTime < now;
                        const isNext = !isPast && (!nextScheduledTime || scheduledTime.getTime() === nextScheduledTime.getTime());
                        
                        return (
                          <div key={index} className={`flex items-center justify-between p-3 rounded-lg ${
                            isPast ? 'bg-gray-100' : isNext ? 'bg-green-100' : 'bg-blue-100'
                          }`}>
                            <span className={`font-medium ${
                              isPast ? 'text-gray-600' : isNext ? 'text-green-800' : 'text-blue-800'
                            }`}>
                              {time}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                              isPast ? 'bg-gray-200 text-gray-700' : 
                              isNext ? 'bg-green-200 text-green-800' : 
                              'bg-blue-200 text-blue-800'
                            }`}>
                              {isPast ? '✅ Đã quét' : isNext ? '⏰ Tiếp theo' : '⏳ Chờ'}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Auto-Scan History */}
                <AutoScanHistorySection />

                {/* Running Status */}
                {autoScanStatus === 'running' && (
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="animate-pulse rounded-full h-3 w-3 bg-green-500" style={{ animationDuration: '2s' }}></div>
                      <h3 className="font-semibold text-green-900">Đang Hoạt Động</h3>
                      <Settings className="h-4 w-4 text-green-600 animate-spin" style={{ animationDuration: '4s' }} />
                      {isAutoScanRunning && (
                        <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-semibold animate-pulse">
                          🔄 Đang quét...
                        </span>
                      )}
                    </div>
                    
                    <p className="text-green-800 mb-4">
                      Hệ thống sẽ tự động quét {autoScanSettings.accounts.length} tài khoản với {autoScanSettings.filters.length + autoScanSettings.templateFilters.length} bộ lọc vào {autoScanSettings.specificTimes.length} thời điểm đã đặt.
                    </p>
                    
                    {isAutoScanRunning && (
                      <div className="bg-yellow-100 rounded-lg p-4 border border-yellow-200 mb-4">
                        <div className="text-center">
                          <div className="text-sm text-yellow-700 mb-1">⚡ Đang quét email...</div>
                          <div className="text-lg font-bold text-yellow-900">
                            Vui lòng chờ trong giây lát
                          </div>
                          <div className="text-xs text-yellow-600 mt-1">
                            Quá trình này có thể mất 1-5 phút
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {nextScheduledTime && !isAutoScanRunning && (
                      <div className="bg-white rounded-lg p-4 border border-green-200">
                        <div className="text-center">
                          <div className="text-sm text-green-700 mb-1">⏰ Thời gian đến lần quét tiếp</div>
                          <div className="text-3xl font-bold text-green-900 font-mono">
                            {getTimeUntilNextScan()}
                          </div>
                          <div className="text-xs text-green-600 mt-1">
                            {timeRemaining && timeRemaining.includes(':') ? 'Giờ:Phút:Giây' : 'Giây'}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Quick Actions */}
                <div className="grid grid-cols-2 gap-3">
                  <button
                    onClick={performAutoScan}
                    disabled={loading || autoScanSettings.accounts.length === 0}
                    className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center justify-center space-x-2 disabled:opacity-50 shadow-md hover:shadow-lg"
                  >
                    <Play className="h-4 w-4" />
                    <span>Chạy Ngay</span>
                  </button>
                  <button
                    onClick={loadAutoScanSettings}
                    className="bg-gradient-to-r from-gray-500 to-slate-500 hover:from-gray-600 hover:to-slate-600 text-white px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg"
                  >
                    <RefreshCw className="h-4 w-4" />
                    <span>Làm Mới</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Status Messages */}
          {activeTab === 'manual' && ((accountSelectionMode === 'single' && selectedAccount) || (accountSelectionMode === 'multiple' && selectedAccounts.length > 0)) && (
            <div className="mt-8">
              {(selectedFilters.length > 0 || selectedTemplateFilters.length > 0) ? (
                <div className="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl p-6 border border-blue-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <CheckCircle className="h-6 w-6 text-blue-600" />
                    <h3 className="font-semibold text-blue-900">Sẵn Sàng Quét!</h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-blue-800 mb-2">📋 Cấu hình quét:</div>
                      <ul className="space-y-1 text-blue-700">
                        <li>• Chế độ: <strong>{accountSelectionMode === 'single' ? 'Một tài khoản' : 'Nhiều tài khoản'}</strong></li>
                        <li>• Tài khoản: <strong>
                          {accountSelectionMode === 'single' 
                            ? '1 tài khoản'
                            : `${selectedAccounts.length} tài khoản`
                          }
                        </strong></li>
                        <li>• Bộ lọc tùy chỉnh: <strong>{selectedFilters.length}</strong></li>
                        <li>• Template thông minh: <strong>{selectedTemplateFilters.length}</strong></li>
                      </ul>
                    </div>
                    <div>
                      <div className="font-medium text-blue-800 mb-2">⚡ Thông tin quét:</div>
                      <ul className="space-y-1 text-blue-700">
                        <li>• Tổng bộ lọc: <strong>{selectedFilters.length + selectedTemplateFilters.length}</strong></li>
                        <li>• Thời gian dự kiến: <strong>1-5 phút</strong></li>
                        <li>• Trạng thái: <strong>Sẵn sàng</strong></li>
                      </ul>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 border border-orange-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <AlertCircle className="h-6 w-6 text-orange-600" />
                    <h3 className="font-semibold text-orange-900">Cần Chọn Bộ Lọc</h3>
                  </div>
                  <p className="text-orange-800">
                    Để đảm bảo quét chính xác, vui lòng chọn ít nhất 1 bộ lọc (tùy chỉnh hoặc template thông minh).
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedScanTab;